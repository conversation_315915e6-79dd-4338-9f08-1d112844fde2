<template>
  <div class="afterSingle">
    <el-form ref="afterSingleForm" :model="afterSingleForm" :rules="afterSingleFormRules" label-width="150px">
      <el-form-item label="追单内容" prop="content">
        <el-input type="textarea" :rows="2" v-model="afterSingleForm.content" style="width: 390px" show-word-limit
          maxlength="255">
        </el-input>
      </el-form-item>
      <el-form-item label="系统名称" prop="systemName" v-if="common.isSender == 1">
        <el-input type="textarea" :rows="2" placeholder="多个系统以逗号分隔" v-model="afterSingleForm.systemName"
          style="width: 390px" show-word-limit maxlength="255">
        </el-input>
      </el-form-item>
      <el-form-item label="附件:">
        <div style="width: 400px">
          <el-tag class="fileName_style" closable v-for="(item, index) in importForm.attachmentFileList" :key="index"
            @close="close(item)" :title="item.name">
            <div class="text-truncate">{{ item.name }}</div>
          </el-tag>
          <el-button size="mini" type="primary" @click="attachmentBrowse">+上传附件</el-button>
        </div>
      </el-form-item>
      <el-form-item label="是否备份中心管辖:" v-if="common.isObject == 1" prop="backCenterControlled" :rules="{
        required: true,
        message: '请选择是否备份中心管辖',
      }">
        <el-radio-group v-model="afterSingleForm.backCenterControlled">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button type="primary" @click="handleSubmit('afterSingleForm')"
        v-loading.fullscreen.lock="afterSingleFullscreenLoading">提 交</el-button>
      <el-button @click="onResetAfterSingleForm">重 置</el-button>
    </div>
    <el-dialog width="420px" title="附件选择" :visible.sync="attachmentDialogVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" append-to-body>
      <file-upload @change="changeFileData" @cancel="closeAttachmentDialog"></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import { apiAfterSingle } from "../api/CommCloudApi";
import { workflowQueryBackControlled } from "@/plugin/backbone/modules/api/generalApi";

import FileUpload from "../../workOrder/components/FileUpload";
import { mixin } from "../../../../../mixins";

export default {
  name: "AfterSingle",
  props: {
    common: Object,
  },
  components: { FileUpload },
  mixins: [mixin],
  data() {
    return {
      afterSingleForm: {
        content: null,
        systemName: null,
        mainSending: null,
        lordSentUserName: null,
        lordSentOrgName: null,
        lordSentUserId: null,
        lordSentOrgId: null,
        cc: null,
        transferStatusRadio: "0",
        backCenterControlled: "0",
        ccUserName: null,
        ccOrgName: null,
        ccUserId: null,
        ccOrgId: null,
        agentManDetail: null,
        copyManDetail: null,
      },
      afterSingleFormRules: {
        content: [
          {
            required: true,
            message: "请填写追单内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "afterSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        systemName: [
          {
            validator: this.checkLength,
            max: 255,
            form: "afterSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      afterSingleFullscreenLoading: false,
      attachmentDialogVisible: false,
      importForm: {
        //附件
        attachmentFileList: [],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "主送",
          ccDetermine: "建单人抄送",
        },
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
        },
      },
      rawAgentManIdArr: [],
      rawAgenManDeptCodeArr: [],
    };
  },
  mounted() {
    if (this.common.isSender == 1) {
      this.afterSingleForm.mainSending = this.stitchingAlgorithm(
        this.common.agentDeptName,
        this.common.agentMan
      );
      this.afterSingleForm.lordSentUserName = this.common.agentMan;
      this.afterSingleForm.lordSentOrgName = this.common.agentDeptName;
      if (this.common.agentManId) {
        this.afterSingleForm.lordSentUserId = this.common.agentManId;
        this.rawAgentManIdArr = this.common.agentManId.split(",");
      }
      if (this.common.agentDeptCode) {
        this.afterSingleForm.lordSentOrgId = this.common.agentDeptCode;
        this.rawAgenManDeptCodeArr = this.common.agentDeptCode.split(",");
      }

      // 调用回填接口
      workflowQueryBackControlled({ woId: this.common.woId })
        .then(backRes => {
          if (backRes.status == "0" && backRes.data) {
            this.afterSingleForm.backCenterControlled = backRes.data;
          }
        })
        .catch(backError => {
          console.log("回填是否备份中心管辖字段失败:", backError);
        });
    }
  },
  watch: {},
  methods: {
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.afterSingleFullscreenLoading = true;
          let formData = new FormData();
          if (this.importForm.attachmentFileList.length > 0) {
            for (let item of this.importForm.attachmentFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          formData.append("isSender", this.common.isSender);
          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: null, //this.common.workItemId,
            processNode: this.common.processNode,
            appendContent: this.afterSingleForm.content,
            sysName: this.afterSingleForm.systemName,
            isSendtoProvince: "0",
            agentManId: "",
            agentMan: "",
            agentDeptCode: "",
            agentDeptName: "",
            copyManId: "",
            copyMan: "",
            copyDeptCode: "",
            copyDeptName: "",
          };
          // 只有在展示"是否备份中心管辖"字段时才传递给接口
          if (this.common.isSender == 1) {
            param.backCenterControlled = this.afterSingleForm.backCenterControlled || "0";
          }
          formData.append("jsonParam", JSON.stringify(param));
          apiAfterSingle(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("追单提交成功");
                this.$emit("closeAfterSingleDialog", this.common.isSender);
              } else {
                this.$message.error("追单提交失败");
              }
              this.afterSingleFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.afterSingleFullscreenLoading = false;
              this.$message.error("追单提交失败");
            });
        } else {
          return false;
        }
      });
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    onResetAfterSingleForm() {
      this.afterSingleForm = {
        content: null,
        systemName: null,
        mainSending: null,
        lordSentUserName: null,
        lordSentOrgName: null,
        lordSentUserId: null,
        lordSentOrgId: null,
        cc: null,
        transferStatusRadio: "0",
        backCenterControlled: "0",
        ccUserName: null,
        ccOrgName: null,
        ccUserId: null,
        ccOrgId: null,
      };
      this.importForm.attachmentFileList = [];
    },
    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.visible = true;
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //主送确定
    lordSentDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        this.afterSingleForm.lordSentUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.afterSingleForm.lordSentUserId = usersCheckedId.join(",");
        this.afterSingleForm.agentManDetail = selectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      } else {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        this.afterSingleForm.lordSentUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.afterSingleForm.lordSentUserId = usersCheckedId.join(",");
        this.afterSingleForm.agentManDetail = usersChecked
          .map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          })
          .join(",");
      }

      let orgsCheckedName = orgsChecked.map(item => {
        return item.name;
      });
      this.afterSingleForm.lordSentOrgName = orgsCheckedName.join(",");
      this.afterSingleForm.mainSending = this.stitchingAlgorithm(
        this.afterSingleForm.lordSentOrgName,
        this.afterSingleForm.agentManDetail
      );
      let orgsCheckedId = orgsChecked.map(item => {
        return item.id;
      });
      this.afterSingleForm.lordSentOrgId = orgsCheckedId.join(",");
    },
    //抄送
    ccDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        this.afterSingleForm.ccUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.afterSingleForm.ccUserId = usersCheckedId.join(",");
        this.afterSingleForm.copyManDetail = selectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      } else {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        this.afterSingleForm.ccUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.afterSingleForm.ccUserId = usersCheckedId.join(",");
        this.afterSingleForm.copyManDetail = usersChecked
          .map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          })
          .join(",");
      }

      let orgsCheckedName = orgsChecked.map(item => {
        return item.name;
      });
      this.afterSingleForm.ccOrgName = orgsCheckedName.join(",");
      this.afterSingleForm.cc = this.stitchingAlgorithm(
        this.afterSingleForm.ccOrgName,
        this.afterSingleForm.copyManDetail
      );
      let orgsCheckedId = orgsChecked.map(item => {
        return item.id;
      });
      this.afterSingleForm.ccOrgId = orgsCheckedId.join(",");
    },
    close(tag) {
      this.importForm.attachmentFileList.splice(
        this.importForm.attachmentFileList.indexOf(tag),
        1
      );
    },
    stitchingAlgorithm(orgName, userName) {
      if (
        null != orgName &&
        orgName.length !== 0 &&
        null != userName &&
        userName.length !== 0
      ) {
        return orgName + "," + userName;
      } else {
        if (null != orgName && orgName.length !== 0) {
          return orgName;
        } else if (null != userName && userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },

    stitchingAlgorithmArr(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName.join(",") + "," + userName.join(",");
      } else {
        if (orgName.length !== 0) {
          return orgName.join(",");
        } else if (userName.length !== 0) {
          return userName.join(",");
        } else {
          return "";
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;

  // 不可全选样式
  .el-tree-node {
    .is-leaf+.el-checkbox .el-checkbox__inner {
      display: inline-block;
    }

    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}

.afterSingle {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 360px;
      vertical-align: top;
    }
  }
}
</style>
